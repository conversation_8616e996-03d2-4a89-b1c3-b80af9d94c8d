"""
应用配置模块
使用 Pydantic BaseSettings 管理配置，支持从环境变量和 .env 文件加载
"""

from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = Field(default="路亚百科API")
    app_version: str = Field(default="0.1.0")
    debug: bool = Field(default=False)
    secret_key: str

    # 数据库配置
    database_host: str
    database_port: int = Field(default=3306)
    database_user: str
    database_password: str
    database_name: str

    # JWT配置
    jwt_secret_key: str
    jwt_algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)
    email_reset_token_expire_hours: int = Field(default=48)

    # API配置
    api_v1_str: str = Field(default="/api/v1")
    backend_cors_origins: list[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"]
    )

    # 日志配置
    log_level: str = Field(default="INFO")
    log_format: str = Field(default="json")

    # 文件上传配置
    max_upload_size: int = Field(default=10485760)  # 10MB
    upload_path: str = Field(default="uploads")

    # 分页配置
    default_page_size: int = Field(default=20)
    max_page_size: int = Field(default=100)
    
    @field_validator("backend_cors_origins", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        """处理CORS origins配置"""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return (
            f"mysql+pymysql://{self.database_user}:{self.database_password}"
            f"@{self.database_host}:{self.database_port}/{self.database_name}"
            f"?charset=utf8mb4"
        )
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )


# 创建全局配置实例
settings = Settings()
